"""
Video generator for lo-fi music videos.
"""

import asyncio
import subprocess
import tempfile
from pathlib import Path
from typing import Op<PERSON>, Tu<PERSON>, List
import json
import random

try:
    from .music_sources.base import Track, LoFiStyle
except ImportError:
    # Fallback for direct execution
    from music_sources.base import Track, LoFiStyle


class LoFiVideoGenerator:
    """Generate lo-fi music videos with visual backgrounds."""
    
    # Style-specific visual themes
    STYLE_VISUALS = {
        LoFiStyle.UPBEAT: {
            'colors': ['#FFB6C1', '#87CEEB', '#98FB98', '#F0E68C'],
            'animation': 'bouncy',
            'elements': ['geometric', 'particles', 'waves']
        },
        LoFiStyle.CALMING: {
            'colors': ['#E6E6FA', '#F0F8FF', '#F5F5DC', '#FFF8DC'],
            'animation': 'slow',
            'elements': ['clouds', 'water', 'gradients']
        },
        LoFiStyle.CHILL: {
            'colors': ['#4682B4', '#708090', '#778899', '#B0C4DE'],
            'animation': 'smooth',
            'elements': ['abstract', 'flowing', 'minimal']
        },
        LoFiStyle.DREAMY: {
            'colors': ['#DDA0DD', '#E6E6FA', '#F0E68C', '#FFB6C1'],
            'animation': 'floating',
            'elements': ['stars', 'nebula', 'soft_shapes']
        },
        LoFiStyle.NOSTALGIC: {
            'colors': ['#D2B48C', '#F5DEB3', '#DEB887', '#CD853F'],
            'animation': 'vintage',
            'elements': ['film_grain', 'sepia', 'old_photos']
        },
        LoFiStyle.FOCUS: {
            'colors': ['#2F4F4F', '#696969', '#708090', '#A9A9A9'],
            'animation': 'steady',
            'elements': ['grid', 'lines', 'minimal']
        },
        LoFiStyle.STUDY: {
            'colors': ['#F5F5F5', '#DCDCDC', '#D3D3D3', '#C0C0C0'],
            'animation': 'subtle',
            'elements': ['books', 'desk', 'minimal']
        },
        LoFiStyle.RELAXING: {
            'colors': ['#98FB98', '#90EE90', '#8FBC8F', '#20B2AA'],
            'animation': 'gentle',
            'elements': ['nature', 'leaves', 'zen']
        },
        LoFiStyle.AMBIENT: {
            'colors': ['#191970', '#483D8B', '#6A5ACD', '#9370DB'],
            'animation': 'atmospheric',
            'elements': ['space', 'atmosphere', 'depth']
        },
        LoFiStyle.JAZZY: {
            'colors': ['#8B4513', '#A0522D', '#CD853F', '#D2691E'],
            'animation': 'rhythmic',
            'elements': ['instruments', 'notes', 'vintage']
        }
    }
    
    def __init__(self, output_dir: str = "videos"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.temp_dir = Path(tempfile.gettempdir()) / "lofi_videos"
        self.temp_dir.mkdir(exist_ok=True)
    
    def check_dependencies(self) -> Tuple[bool, List[str]]:
        """Check if required dependencies are installed."""
        missing = []
        
        # Check for FFmpeg
        try:
            subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing.append('ffmpeg')
        
        return len(missing) == 0, missing
    
    async def generate_video(
        self, 
        track: Track, 
        audio_data: bytes, 
        duration: float, 
        style: LoFiStyle,
        output_filename: Optional[str] = None
    ) -> Optional[Path]:
        """Generate a lo-fi video with the given track and style."""
        
        if not output_filename:
            safe_title = "".join(c for c in track.title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            output_filename = f"{safe_title}_{style.value}_{int(duration)}s.mp4"
        
        output_path = self.output_dir / output_filename
        
        # Create temporary audio file
        audio_file = self.temp_dir / f"audio_{track.id}.mp3"
        with open(audio_file, 'wb') as f:
            f.write(audio_data)
        
        try:
            # Generate visual background
            visual_path = await self._generate_visual_background(style, duration)
            
            if not visual_path:
                # Fallback to simple color background
                visual_path = await self._generate_simple_background(style, duration)
            
            # Combine audio and visual
            success = await self._combine_audio_visual(
                audio_file, visual_path, output_path, duration, track, style
            )
            
            if success:
                return output_path
            else:
                return None
                
        finally:
            # Cleanup temporary files
            if audio_file.exists():
                audio_file.unlink()
    
    async def _generate_visual_background(self, style: LoFiStyle, duration: float) -> Optional[Path]:
        """Generate visual background based on style."""
        visual_config = self.STYLE_VISUALS.get(style, self.STYLE_VISUALS[LoFiStyle.CHILL])
        
        # For now, create a simple animated background using FFmpeg
        # In a full implementation, you might use more sophisticated tools
        return await self._create_animated_background(visual_config, duration)
    
    async def _create_animated_background(self, visual_config: dict, duration: float) -> Optional[Path]:
        """Create animated background using FFmpeg filters."""
        output_path = self.temp_dir / f"background_{random.randint(1000, 9999)}.mp4"
        
        # Get primary color for the style
        primary_color = visual_config['colors'][0].replace('#', '')
        secondary_color = visual_config['colors'][1].replace('#', '') if len(visual_config['colors']) > 1 else primary_color
        
        # Create animated gradient background
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=0x{primary_color}:size=1920x1080:duration={duration}',
            '-f', 'lavfi', 
            '-i', f'color=c=0x{secondary_color}:size=1920x1080:duration={duration}',
            '-filter_complex', 
            f'[0][1]blend=all_mode=overlay:all_opacity=0.5,format=yuv420p',
            '-t', str(duration),
            '-r', '30',
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return output_path
            else:
                print(f"FFmpeg error: {stderr.decode()}")
                return None
                
        except Exception as e:
            print(f"Error creating background: {e}")
            return None
    
    async def _generate_simple_background(self, style: LoFiStyle, duration: float) -> Optional[Path]:
        """Generate simple solid color background as fallback."""
        visual_config = self.STYLE_VISUALS.get(style, self.STYLE_VISUALS[LoFiStyle.CHILL])
        color = visual_config['colors'][0].replace('#', '')
        
        output_path = self.temp_dir / f"simple_bg_{random.randint(1000, 9999)}.mp4"
        
        cmd = [
            'ffmpeg', '-y',
            '-f', 'lavfi',
            '-i', f'color=c=0x{color}:size=1920x1080:duration={duration}',
            '-t', str(duration),
            '-r', '30',
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return output_path
            else:
                return None
                
        except Exception as e:
            print(f"Error creating simple background: {e}")
            return None
    
    async def _combine_audio_visual(
        self, 
        audio_path: Path, 
        visual_path: Path, 
        output_path: Path, 
        duration: float,
        track: Track,
        style: LoFiStyle
    ) -> bool:
        """Combine audio and visual into final video."""
        
        # Add text overlay with track info
        title_text = track.title.replace("'", "\\'").replace('"', '\\"')
        artist_text = track.artist.replace("'", "\\'").replace('"', '\\"')
        
        # Create text overlay filter
        text_filter = (
            f"drawtext=text='{title_text}':fontcolor=white:fontsize=48:"
            f"x=(w-text_w)/2:y=h/2-100:enable='between(t,2,8)',"
            f"drawtext=text='by {artist_text}':fontcolor=white:fontsize=32:"
            f"x=(w-text_w)/2:y=h/2-40:enable='between(t,2,8)'"
        )
        
        cmd = [
            'ffmpeg', '-y',
            '-i', str(visual_path),
            '-i', str(audio_path),
            '-filter_complex', text_filter,
            '-c:v', 'libx264',
            '-c:a', 'aac',
            '-shortest',
            '-t', str(duration),
            str(output_path)
        ]
        
        try:
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0 and output_path.exists():
                return True
            else:
                print(f"FFmpeg combine error: {stderr.decode()}")
                return False
                
        except Exception as e:
            print(f"Error combining audio/visual: {e}")
            return False
    
    def get_video_info(self, video_path: Path) -> Optional[dict]:
        """Get information about generated video."""
        if not video_path.exists():
            return None
        
        try:
            cmd = [
                'ffprobe', '-v', 'quiet', '-print_format', 'json',
                '-show_format', '-show_streams', str(video_path)
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, check=True)
            return json.loads(result.stdout)
            
        except (subprocess.CalledProcessError, json.JSONDecodeError):
            return None
    
    def cleanup_temp_files(self):
        """Clean up temporary files."""
        for file in self.temp_dir.glob("*"):
            try:
                file.unlink()
            except Exception:
                pass
