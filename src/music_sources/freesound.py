"""
Freesound.org API client for lo-fi music discovery.
"""

import asyncio
import aiohttp
from typing import List, Optional, Dict, Any
from urllib.parse import urlencode

from .base import (
    MusicSource, Track, SearchQuery, SearchResult, LicenseType,
    MusicSourceError, RateLimitError, AuthenticationError, RateLimiter
)


class FreesoundClient(MusicSource):
    """Client for Freesound.org API v2."""
    
    BASE_URL = "https://freesound.org/apiv2"
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key, **kwargs)
        self._rate_limiter = RateLimiter(requests_per_minute=60)
        self.session = None
    
    @property
    def source_name(self) -> str:
        return "Freesound"
    
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session."""
        if self.session is None:
            self.session = aiohttp.ClientSession()
        return self.session
    
    async def _make_request(self, endpoint: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """Make authenticated request to Freesound API."""
        await self._rate_limiter.acquire()
        
        if params is None:
            params = {}
        
        params['token'] = self.api_key
        url = f"{self.BASE_URL}/{endpoint.lstrip('/')}"
        
        session = await self._get_session()
        
        try:
            async with session.get(url, params=params) as response:
                if response.status == 429:
                    retry_after = response.headers.get('Retry-After')
                    raise RateLimitError(
                        "Rate limit exceeded", 
                        retry_after=int(retry_after) if retry_after else None
                    )
                elif response.status == 401:
                    raise AuthenticationError("Invalid API key")
                elif response.status != 200:
                    raise MusicSourceError(f"API request failed: {response.status}")
                
                return await response.json()
        
        except aiohttp.ClientError as e:
            raise MusicSourceError(f"Network error: {e}")
    
    def _parse_license(self, license_name: str) -> LicenseType:
        """Parse Freesound license to our LicenseType enum."""
        license_map = {
            "Creative Commons 0": LicenseType.CC0,
            "Attribution": LicenseType.CC_BY,
            "Attribution Noncommercial": LicenseType.CC_BY_NC,
        }
        return license_map.get(license_name, LicenseType.CUSTOM)
    
    def _parse_track(self, sound_data: Dict[str, Any]) -> Track:
        """Parse Freesound API response to Track object."""
        # Extract relevant information from Freesound sound object
        track_id = str(sound_data.get('id', ''))
        title = sound_data.get('name', 'Unknown')
        artist = sound_data.get('username', 'Unknown')
        duration = float(sound_data.get('duration', 0))
        
        # Get preview URLs
        previews = sound_data.get('previews', {})
        preview_url = previews.get('preview-hq-mp3') or previews.get('preview-lq-mp3')
        
        # License information
        license_name = sound_data.get('license', '')
        license_type = self._parse_license(license_name)
        
        # Tags and metadata
        tags = sound_data.get('tags', [])
        description = sound_data.get('description', '')
        
        # Generate attribution text
        attribution_text = None
        if license_type in [LicenseType.CC_BY, LicenseType.CC_BY_NC]:
            sound_url = f"https://freesound.org/s/{track_id}/"
            attribution_text = f'"{title}" by {artist} ({sound_url}) licensed under {license_name}'
        
        return Track(
            id=track_id,
            title=title,
            artist=artist,
            duration=duration,
            preview_url=preview_url,
            download_url=sound_data.get('download'),  # Requires OAuth2
            license_type=license_type,
            attribution_text=attribution_text,
            tags=tags,
            source=self.source_name,
            file_format=sound_data.get('type'),
            file_size=sound_data.get('filesize')
        )
    
    async def search(self, query: SearchQuery) -> SearchResult:
        """Search for lo-fi tracks on Freesound."""
        # Build search parameters
        params = {
            'page_size': min(query.limit, 150),  # Freesound max is 150
            'page': (query.offset // query.limit) + 1 if query.limit > 0 else 1,
            'fields': 'id,name,username,duration,previews,license,tags,description,type,filesize,download'
        }
        
        # Build query string
        search_terms = []
        
        if query.query:
            search_terms.append(query.query)
        
        # Add lo-fi specific terms if no specific query
        if not query.query:
            search_terms.extend(['lofi', 'lo-fi', 'chill', 'ambient', 'beats'])
        
        if query.genre:
            search_terms.append(f'tag:{query.genre}')
        
        if query.mood:
            search_terms.append(f'tag:{query.mood}')
        
        for tag in query.tags:
            search_terms.append(f'tag:{tag}')
        
        params['query'] = ' '.join(search_terms)
        
        # Add filters
        filters = []
        
        if query.min_duration:
            filters.append(f'duration:[{query.min_duration} TO *]')
        
        if query.max_duration:
            filters.append(f'duration:[* TO {query.max_duration}]')
        
        # License filters
        if query.license_types:
            license_names = []
            for license_type in query.license_types:
                if license_type == LicenseType.CC0:
                    license_names.append('"Creative Commons 0"')
                elif license_type == LicenseType.CC_BY:
                    license_names.append('"Attribution"')
                elif license_type == LicenseType.CC_BY_NC:
                    license_names.append('"Attribution Noncommercial"')
            
            if license_names:
                filters.append(f'license:({" OR ".join(license_names)})')
        
        if filters:
            params['filter'] = ' '.join(filters)
        
        # Make API request
        try:
            response = await self._make_request('search/text/', params)
            
            # Parse results
            tracks = []
            for sound_data in response.get('results', []):
                try:
                    track = self._parse_track(sound_data)
                    tracks.append(track)
                except Exception as e:
                    # Log error but continue with other tracks
                    print(f"Error parsing track {sound_data.get('id')}: {e}")
                    continue
            
            total_count = response.get('count', 0)
            has_more = response.get('next') is not None
            next_offset = query.offset + len(tracks) if has_more else None
            
            return SearchResult(
                tracks=tracks,
                total_count=total_count,
                has_more=has_more,
                next_offset=next_offset
            )
        
        except Exception as e:
            raise MusicSourceError(f"Search failed: {e}")
    
    async def get_track(self, track_id: str) -> Optional[Track]:
        """Get detailed information about a specific track."""
        try:
            response = await self._make_request(f'sounds/{track_id}/')
            return self._parse_track(response)
        except MusicSourceError:
            return None
    
    async def download_preview(self, track: Track) -> Optional[bytes]:
        """Download preview audio data."""
        if not track.preview_url:
            return None
        
        session = await self._get_session()
        try:
            async with session.get(track.preview_url) as response:
                if response.status == 200:
                    return await response.read()
        except aiohttp.ClientError:
            pass
        
        return None
