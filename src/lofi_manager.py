"""
Specialized lo-fi music manager for video generation.
"""

import asyncio
from typing import List, Optional, Dict, Any
from pathlib import Path
import random

try:
    from .music_sources.manager import MusicSourceManager
    from .music_sources.base import SearchQuery, Track, LicenseType, LoFiStyle, LOFI_STYLE_KEYWORDS
except ImportError:
    # Fallback for direct execution
    from music_sources.manager import MusicSourceManager
    from music_sources.base import SearchQuery, Track, LicenseType, LoFiStyle, LOFI_STYLE_KEYWORDS


class LoFiMusicManager:
    """Specialized manager for lo-fi music discovery and curation."""

    # Use style keywords from base module
    STYLE_KEYWORDS = LOFI_STYLE_KEYWORDS
    
    # BPM ranges for different styles
    STYLE_BPM_RANGES = {
        LoFiStyle.UPBEAT: (90, 120),
        LoFiStyle.CALMING: (60, 80),
        LoFiStyle.CHILL: (70, 90),
        LoFiStyle.DREAMY: (50, 70),
        LoFiStyle.NOSTALGIC: (70, 95),
        LoFiStyle.FOCUS: (80, 100),
        LoFiStyle.STUDY: (65, 85),
        LoFiStyle.RELAXING: (50, 75),
        LoFiStyle.AMBIENT: (40, 70),
        LoFiStyle.JAZZY: (80, 110)
    }
    
    def __init__(self, cache_dir: str = "lofi_cache"):
        self.manager = MusicSourceManager(cache_dir)
        self._curated_tracks: Dict[LoFiStyle, List[Track]] = {}
    
    def configure_sources(self, freesound_api_key: str, **kwargs):
        """Configure music sources."""
        self.manager.configure_freesound(freesound_api_key)
        # Add other sources as needed
    
    async def find_lofi_for_style(self, style: LoFiStyle, duration: float, limit: int = 20) -> List[Track]:
        """Find lo-fi tracks matching a specific style and duration."""
        
        # Build style-specific search query
        style_keywords = self.STYLE_KEYWORDS.get(style, [])
        
        # Create search query with style-specific terms
        query = SearchQuery(
            query=f"lofi {style.value} {' '.join(style_keywords[:3])}",
            style=style,
            min_duration=max(30.0, duration * 0.8),  # At least 80% of requested duration
            max_duration=duration * 2.0,  # Up to 2x requested duration for flexibility
            license_types=[LicenseType.CC0, LicenseType.CC_BY],  # Commercial-friendly only
            tags=['lofi', 'lo-fi', style.value] + style_keywords[:2],
            limit=limit,
            lofi_only=True
        )
        
        # Search all sources
        results = await self.manager.search_all_sources(query)
        
        # Combine and filter results
        all_tracks = []
        for source_name, result in results.items():
            all_tracks.extend(result.tracks)
        
        # Filter for lo-fi characteristics
        lofi_tracks = self._filter_lofi_tracks(all_tracks, style)
        
        # Sort by relevance to style and duration
        lofi_tracks.sort(key=lambda t: self._calculate_style_score(t, style, duration), reverse=True)
        
        return lofi_tracks[:limit]
    
    def _filter_lofi_tracks(self, tracks: List[Track], style: LoFiStyle) -> List[Track]:
        """Filter tracks to ensure they are actually lo-fi."""
        lofi_tracks = []
        
        for track in tracks:
            if self._is_lofi_track(track, style):
                lofi_tracks.append(track)
        
        return lofi_tracks
    
    def _is_lofi_track(self, track: Track, style: LoFiStyle) -> bool:
        """Check if a track is actually lo-fi music."""
        # Check tags for lo-fi indicators
        lofi_indicators = [
            'lofi', 'lo-fi', 'chill', 'ambient', 'beats', 'hip-hop',
            'downtempo', 'chillhop', 'jazzhop', 'study', 'relax'
        ]
        
        track_tags_lower = [tag.lower() for tag in track.tags]
        title_lower = track.title.lower()
        
        # Must have at least one lo-fi indicator
        has_lofi_indicator = any(
            indicator in track_tags_lower or indicator in title_lower
            for indicator in lofi_indicators
        )
        
        if not has_lofi_indicator:
            return False
        
        # Exclude non-lo-fi genres
        excluded_genres = [
            'rock', 'metal', 'punk', 'techno', 'house', 'trance',
            'dubstep', 'drum and bass', 'hardcore', 'classical'
        ]
        
        has_excluded_genre = any(
            genre in track_tags_lower or genre in title_lower
            for genre in excluded_genres
        )
        
        if has_excluded_genre:
            return False
        
        # Duration check (lo-fi tracks are usually 1-10 minutes)
        if track.duration < 30 or track.duration > 600:
            return False
        
        return True
    
    def _calculate_style_score(self, track: Track, style: LoFiStyle, target_duration: float) -> float:
        """Calculate how well a track matches the requested style."""
        score = 0.0
        
        # Style keyword matching
        style_keywords = self.STYLE_KEYWORDS.get(style, [])
        track_text = f"{track.title} {' '.join(track.tags)}".lower()
        
        for keyword in style_keywords:
            if keyword in track_text:
                score += 1.0
        
        # Duration preference (prefer tracks close to target duration)
        duration_diff = abs(track.duration - target_duration)
        duration_score = max(0, 1.0 - (duration_diff / target_duration))
        score += duration_score * 2.0
        
        # License preference (CC0 is better than CC-BY)
        if track.license_type == LicenseType.CC0:
            score += 0.5
        
        # Source preference (some sources might be more reliable)
        if track.source == "Freesound":
            score += 0.3
        
        return score
    
    async def get_track_for_video(self, duration: float, style: LoFiStyle) -> Optional[Track]:
        """Get the best lo-fi track for a video of specified duration and style."""
        
        # Try to find tracks that match the duration closely
        tracks = await self.find_lofi_for_style(style, duration, limit=10)
        
        if not tracks:
            # Fallback to any chill lo-fi if no style-specific tracks found
            fallback_query = SearchQuery(
                query="lofi chill ambient",
                min_duration=max(30.0, duration * 0.5),
                max_duration=duration * 3.0,
                license_types=[LicenseType.CC0, LicenseType.CC_BY],
                limit=5,
                lofi_only=True
            )
            
            results = await self.manager.search_all_sources(fallback_query)
            tracks = []
            for result in results.values():
                tracks.extend(result.tracks)
        
        if not tracks:
            return None
        
        # Select the best matching track
        best_track = max(tracks, key=lambda t: self._calculate_style_score(t, style, duration))
        
        return best_track
    
    async def download_track_audio(self, track: Track) -> Optional[bytes]:
        """Download audio data for a track."""
        return await self.manager.get_audio_preview(track)
    
    def get_attribution_text(self, track: Track) -> Optional[str]:
        """Get attribution text for a track."""
        return self.manager.get_attribution_requirements([track])
    
    async def curate_style_playlist(self, style: LoFiStyle, count: int = 50) -> List[Track]:
        """Curate a playlist of tracks for a specific style."""
        if style in self._curated_tracks and len(self._curated_tracks[style]) >= count:
            return random.sample(self._curated_tracks[style], min(count, len(self._curated_tracks[style])))
        
        # Find tracks for this style with various durations
        all_tracks = []
        for duration in [120, 180, 240, 300]:  # 2, 3, 4, 5 minutes
            tracks = await self.find_lofi_for_style(style, duration, limit=15)
            all_tracks.extend(tracks)
        
        # Deduplicate
        seen = set()
        unique_tracks = []
        for track in all_tracks:
            track_key = f"{track.title}:{track.artist}"
            if track_key not in seen:
                seen.add(track_key)
                unique_tracks.append(track)
        
        # Cache the curated tracks
        self._curated_tracks[style] = unique_tracks
        
        return unique_tracks[:count]
    
    async def close(self):
        """Close the manager."""
        await self.manager.close()
    
    @staticmethod
    def get_available_styles() -> List[str]:
        """Get list of available lo-fi styles."""
        return [style.value for style in LoFiStyle]
    
    @staticmethod
    def parse_style(style_str: str) -> Optional[LoFiStyle]:
        """Parse style string to LoFiStyle enum."""
        try:
            return LoFiStyle(style_str.lower())
        except ValueError:
            return None
