# Lo-Fi Channel

A Python application for discovering and using royalty-free lo-fi music from multiple sources.

## Features

- 🎵 **Multiple Music Sources**: Freesound.org, Pixabay, and more
- 📜 **License Compliance**: Automatic license detection and attribution
- 💾 **Smart Caching**: Local caching for better performance
- 🔍 **Advanced Search**: Filter by mood, genre, duration, and license
- 🎧 **Preview Downloads**: Stream or download preview tracks
- ⚖️ **Commercial Use**: Filter tracks suitable for commercial projects

## Supported Music Sources

### 1. Freesound.org ⭐ (Primary)
- **License**: Creative Commons (CC0, CC-BY, CC-BY-NC)
- **API**: Full APIv2 support
- **Content**: Extensive lo-fi and ambient collection
- **Rate Limits**: 60 requests/minute, 2000/day

### 2. Pixabay Music
- **License**: Pixabay Content License (commercial use allowed)
- **API**: Available
- **Content**: 139+ categorized lo-fi tracks
- **Rate Limits**: Varies by account type

### 3. Mubert AI (Premium)
- **License**: Royalty-free for subscribers
- **API**: Available
- **Content**: AI-generated lo-fi on demand
- **Rate Limits**: Based on subscription

## Quick Start

1. **Install dependencies**:
   ```bash
   pip install -e .
   ```

2. **Get API keys**:
   - Freesound: https://freesound.org/apiv2/apply
   - Pixabay: https://pixabay.com/api/docs/

3. **Configure environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys
   ```

4. **Run the example**:
   ```bash
   python example_usage.py
   ```

## Usage

```python
import asyncio
from src.music_sources.manager import MusicSourceManager
from src.music_sources.base import SearchQuery, LicenseType

async def main():
    # Initialize manager
    manager = MusicSourceManager()
    manager.configure_freesound("your_api_key")

    # Discover lo-fi tracks
    tracks = await manager.discover_lofi_tracks(limit=10)

    # Search with specific criteria
    query = SearchQuery(
        query="chill ambient",
        license_types=[LicenseType.CC0, LicenseType.CC_BY],
        min_duration=120.0
    )
    results = await manager.search_all_sources(query)

    # Download preview
    if tracks:
        audio_data = await manager.get_audio_preview(tracks[0])

    await manager.close()

asyncio.run(main())
```

## License Compliance

The application automatically handles license compliance:

- **CC0**: Public domain, no attribution required
- **CC-BY**: Attribution required, commercial use allowed
- **CC-BY-NC**: Attribution required, non-commercial only

Use `get_attribution_requirements()` to get proper attribution text.

## Development

Install development dependencies:
```bash
pip install -e ".[dev]"
```

Run tests:
```bash
pytest
```

Format code:
```bash
black src/ example_usage.py
```

## API Rate Limits

- **Freesound**: 60 requests/minute, 2000/day
- **Pixabay**: Varies by account
- **Mubert**: Based on subscription

The application includes automatic rate limiting and retry logic.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## Legal Notice

This application helps you find and use royalty-free music, but you are responsible for:
- Verifying license terms
- Providing proper attribution when required
- Complying with usage restrictions
- Respecting rate limits and terms of service

## Support

For issues and questions:
- Check the documentation
- Review API documentation for each source
- Open an issue on GitHub