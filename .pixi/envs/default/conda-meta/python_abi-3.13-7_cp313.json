{"build": "7_cp313", "build_number": 7, "constrains": ["python 3.13.* *_cp313"], "depends": [], "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "md5": "e84b44e6300f1703cb25d29120c5b1d8", "name": "python_abi", "noarch": "generic", "purls": [], "sha256": "0595134584589064f56e67d3de1d8fcbb673a972946bce25fb593fb092fdcd97", "size": 6988, "subdir": "noarch", "timestamp": 1745258852285, "version": "3.13", "fn": "python_abi-3.13-7_cp313.conda", "url": "https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda", "channel": "https://conda.anaconda.org/conda-forge/", "extracted_package_dir": "/home/<USER>/.cache/rattler/cache/pkgs/python_abi-3.13-7_cp313", "files": [], "paths_data": {"paths_version": 1, "paths": []}, "link": {"source": "/home/<USER>/.cache/rattler/cache/pkgs/python_abi-3.13-7_cp313", "type": 1}}