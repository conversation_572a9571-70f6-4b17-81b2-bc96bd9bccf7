{"build": "hb9d3cd8_0", "build_number": 0, "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=13"], "license": "BSD-2-<PERSON><PERSON>", "license_family": "BSD", "md5": "c7e925f37e3b40d893459e625f6a53f1", "name": "libmpdec", "purls": [], "sha256": "3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee", "size": 91183, "subdir": "linux-64", "timestamp": 1748393666725, "version": "4.0.0", "fn": "libmpdec-4.0.0-hb9d3cd8_0.conda", "url": "https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda", "channel": "https://conda.anaconda.org/conda-forge/", "extracted_package_dir": "/home/<USER>/.cache/rattler/cache/pkgs/libmpdec-4.0.0-hb9d3cd8_0", "files": ["lib/libmpdec.so.4", "lib/libmpdec.so.4.0.0"], "paths_data": {"paths_version": 1, "paths": [{"_path": "lib/libmpdec.so.4", "path_type": "softlink", "sha256": "e3e03d8738eac993fef7ed2aa3f1d171e049d522659118c00f8064d47ebb8827", "sha256_in_prefix": "0452ea4e2a963b81b8dcd2d0531a0c5bd6d08a3c7a82ba2210199c0a4fba6ddc", "size_in_bytes": 229552}, {"_path": "lib/libmpdec.so.4.0.0", "path_type": "hardlink", "sha256": "e3e03d8738eac993fef7ed2aa3f1d171e049d522659118c00f8064d47ebb8827", "sha256_in_prefix": "e3e03d8738eac993fef7ed2aa3f1d171e049d522659118c00f8064d47ebb8827", "size_in_bytes": 229552}]}, "link": {"source": "/home/<USER>/.cache/rattler/cache/pkgs/libmpdec-4.0.0-hb9d3cd8_0", "type": 1}}