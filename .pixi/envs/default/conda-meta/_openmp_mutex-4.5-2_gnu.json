{"build": "2_gnu", "build_number": 16, "constrains": ["openmp_impl 9999"], "depends": ["_libgcc_mutex 0.1 conda_forge", "libgomp >=7.5.0"], "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "md5": "73aaf86a425cc6e73fcf236a5a46396d", "name": "_openmp_mutex", "purls": [], "sha256": "fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22", "size": 23621, "subdir": "linux-64", "timestamp": 1650670423406, "version": "4.5", "fn": "_openmp_mutex-4.5-2_gnu.tar.bz2", "url": "https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2", "channel": "https://conda.anaconda.org/conda-forge/", "extracted_package_dir": "/home/<USER>/.cache/rattler/cache/pkgs/_openmp_mutex-4.5-2_gnu", "files": ["lib/libgomp.so.1"], "paths_data": {"paths_version": 1, "paths": [{"_path": "lib/libgomp.so.1", "path_type": "softlink", "sha256": "393cfda114a20f70573b52bd4293fe3455afa0ad7a4e118a8e5f316624f446c2", "sha256_in_prefix": "83b4bd70546567c74bca5c44f4dbebf046e08053f98c26079f1a4b8f5ef07bc1", "size_in_bytes": 921800}]}, "link": {"source": "/home/<USER>/.cache/rattler/cache/pkgs/_openmp_mutex-4.5-2_gnu", "type": 1}}