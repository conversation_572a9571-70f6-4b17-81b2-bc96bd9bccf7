{"build": "h0b41bf4_0", "build_number": 0, "depends": ["libgcc-ng >=12"], "license": "BSD-3-<PERSON><PERSON>", "license_family": "BSD", "md5": "40b61aab5c7ba9ff276c41cfffe6b80b", "name": "libuuid", "purls": [], "sha256": "787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18", "size": 33601, "subdir": "linux-64", "timestamp": 1680112270483, "version": "2.38.1", "fn": "libuuid-2.38.1-h0b41bf4_0.conda", "url": "https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda", "channel": "https://conda.anaconda.org/conda-forge/", "extracted_package_dir": "/home/<USER>/.cache/rattler/cache/pkgs/libuuid-2.38.1-h0b41bf4_0", "files": ["include/uuid/uuid.h", "lib/libuuid.a", "lib/libuuid.so", "lib/libuuid.so.1", "lib/libuuid.so.1.3.0", "lib/pkgconfig/uuid.pc"], "paths_data": {"paths_version": 1, "paths": [{"_path": "include/uuid/uuid.h", "path_type": "hardlink", "sha256": "883bef35f0766a9d520bf9cfde86bea86c1dc47a675f68fae3cb1f2dcbe3088d", "sha256_in_prefix": "883bef35f0766a9d520bf9cfde86bea86c1dc47a675f68fae3cb1f2dcbe3088d", "size_in_bytes": 4041}, {"_path": "lib/libuuid.a", "path_type": "hardlink", "sha256": "153e2611c38cd5aebfb3174df3dffbdcc835cb0a3e610d9c02db315a5f96b9c1", "sha256_in_prefix": "153e2611c38cd5aebfb3174df3dffbdcc835cb0a3e610d9c02db315a5f96b9c1", "size_in_bytes": 53770}, {"_path": "lib/libuuid.so", "path_type": "softlink", "sha256": "40d0abac00d276a46ad63e301b35c8fb4cb2688ec5c85e77a836f8edb49c6da4", "sha256_in_prefix": "5106e5a2079097d6cf84a73ae5d9dd53a3279a06006e7616d60be7afdb144556", "size_in_bytes": 36064}, {"_path": "lib/libuuid.so.1", "path_type": "softlink", "sha256": "40d0abac00d276a46ad63e301b35c8fb4cb2688ec5c85e77a836f8edb49c6da4", "sha256_in_prefix": "5106e5a2079097d6cf84a73ae5d9dd53a3279a06006e7616d60be7afdb144556", "size_in_bytes": 36064}, {"_path": "lib/libuuid.so.1.3.0", "path_type": "hardlink", "sha256": "40d0abac00d276a46ad63e301b35c8fb4cb2688ec5c85e77a836f8edb49c6da4", "sha256_in_prefix": "40d0abac00d276a46ad63e301b35c8fb4cb2688ec5c85e77a836f8edb49c6da4", "size_in_bytes": 36064}, {"_path": "lib/pkgconfig/uuid.pc", "path_type": "hardlink", "sha256": "4def02521991cbbb9a483a63d63b59cdb637ed420b4a2ce3cf88aadf8f27408b", "sha256_in_prefix": "8308bdfd49961fd53b8ba32df1034aca55b87dcb2cffc5ca62f27b134a42f73c", "size_in_bytes": 376, "file_mode": "text", "prefix_placeholder": "/home/<USER>/feedstock_root/build_artifacts/libuuid_1680112112937/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_pla"}]}, "link": {"source": "/home/<USER>/.cache/rattler/cache/pkgs/libuuid-2.38.1-h0b41bf4_0", "type": 1}}