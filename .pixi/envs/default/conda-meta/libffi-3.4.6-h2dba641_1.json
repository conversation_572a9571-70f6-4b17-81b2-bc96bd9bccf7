{"build": "h2dba641_1", "build_number": 1, "depends": ["__glibc >=2.17,<3.0.a0", "libgcc >=13"], "license": "MIT", "license_family": "MIT", "md5": "ede4673863426c0883c0063d853bbd85", "name": "libffi", "purls": [], "sha256": "764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab", "size": 57433, "subdir": "linux-64", "timestamp": 1743434498161, "version": "3.4.6", "fn": "libffi-3.4.6-h2dba641_1.conda", "url": "https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda", "channel": "https://conda.anaconda.org/conda-forge/", "extracted_package_dir": "/home/<USER>/.cache/rattler/cache/pkgs/libffi-3.4.6-h2dba641_1", "files": ["lib/libffi.a", "lib/libffi.so", "lib/libffi.so.8", "lib/libffi.so.8.1.4", "include/ffi.h", "include/ffitarget.h", "share/man/man3/ffi.3", "share/man/man3/ffi_call.3", "share/man/man3/ffi_prep_cif.3", "share/man/man3/ffi_prep_cif_var.3", "lib/pkgconfig/libffi.pc", "share/info/libffi.info"], "paths_data": {"paths_version": 1, "paths": [{"_path": "lib/libffi.a", "path_type": "hardlink", "sha256": "202207513ea5f1e3f7a2fbe0a560a6afb5f8419343ca3e4185dd8738673bee86", "sha256_in_prefix": "202207513ea5f1e3f7a2fbe0a560a6afb5f8419343ca3e4185dd8738673bee86", "size_in_bytes": 77882}, {"_path": "lib/libffi.so", "path_type": "softlink", "sha256": "6523475fdb72c8324e6c26152463674878a4ca6f34c40d77139fd19ea025286c", "sha256_in_prefix": "f0785b608997dfa520d3ac3ef845cd748ef6e1d31adcf8087af2259c4cc031d4", "size_in_bytes": 50528}, {"_path": "lib/libffi.so.8", "path_type": "softlink", "sha256": "6523475fdb72c8324e6c26152463674878a4ca6f34c40d77139fd19ea025286c", "sha256_in_prefix": "f0785b608997dfa520d3ac3ef845cd748ef6e1d31adcf8087af2259c4cc031d4", "size_in_bytes": 50528}, {"_path": "lib/libffi.so.8.1.4", "path_type": "hardlink", "sha256": "6523475fdb72c8324e6c26152463674878a4ca6f34c40d77139fd19ea025286c", "sha256_in_prefix": "6523475fdb72c8324e6c26152463674878a4ca6f34c40d77139fd19ea025286c", "size_in_bytes": 50528}, {"_path": "include/ffi.h", "path_type": "hardlink", "sha256": "d5ced501c119b91cfb29161a4f037cf9350bfbca6fd034df838333effb843687", "sha256_in_prefix": "d5ced501c119b91cfb29161a4f037cf9350bfbca6fd034df838333effb843687", "size_in_bytes": 14241}, {"_path": "include/ffitarget.h", "path_type": "hardlink", "sha256": "45e4fd2585aaed711e4fa3d1377b70c8e54dcdc56bdf402fa23a8816d19cc58a", "sha256_in_prefix": "45e4fd2585aaed711e4fa3d1377b70c8e54dcdc56bdf402fa23a8816d19cc58a", "size_in_bytes": 4928}, {"_path": "share/man/man3/ffi.3", "path_type": "hardlink", "sha256": "aa4730e114c305943a2226a524ed8447dc6b66a184523999868e5433c2c9de74", "sha256_in_prefix": "aa4730e114c305943a2226a524ed8447dc6b66a184523999868e5433c2c9de74", "size_in_bytes": 850}, {"_path": "share/man/man3/ffi_call.3", "path_type": "hardlink", "sha256": "2817ce7b78cb737d7b85b18b45899470f5f565f990d056d3d8cfabf6d779477f", "sha256_in_prefix": "2817ce7b78cb737d7b85b18b45899470f5f565f990d056d3d8cfabf6d779477f", "size_in_bytes": 2333}, {"_path": "share/man/man3/ffi_prep_cif.3", "path_type": "hardlink", "sha256": "f60c5bb9d04b55988da13511a2c3edfa0f39fb6f51abfb8ac24d0b161c4169c0", "sha256_in_prefix": "f60c5bb9d04b55988da13511a2c3edfa0f39fb6f51abfb8ac24d0b161c4169c0", "size_in_bytes": 1158}, {"_path": "share/man/man3/ffi_prep_cif_var.3", "path_type": "hardlink", "sha256": "9365685252f33f13627c9303bc01883b764227132069260c19e94100ff442a51", "sha256_in_prefix": "9365685252f33f13627c9303bc01883b764227132069260c19e94100ff442a51", "size_in_bytes": 1321}, {"_path": "lib/pkgconfig/libffi.pc", "path_type": "hardlink", "sha256": "93fd10c8a332697ed7f873b66bf59da6deccc20f43ce5fd4ff6e7554ec974605", "sha256_in_prefix": "51f6e12047494bb90d8e99a89b897702b75360ab48633fc7021606b17ae4686a", "size_in_bytes": 340, "file_mode": "text", "prefix_placeholder": "/home/<USER>/feedstock_root/build_artifacts/libffi_1743434367992/_h_env_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_placehold_plac"}, {"_path": "share/info/libffi.info", "path_type": "hardlink", "sha256": "eb6d3bbcefff86cfb76ef107c529ae58d97c5ba4e88334750a96a2679f020630", "sha256_in_prefix": "eb6d3bbcefff86cfb76ef107c529ae58d97c5ba4e88334750a96a2679f020630", "size_in_bytes": 39182}]}, "link": {"source": "/home/<USER>/.cache/rattler/cache/pkgs/libffi-3.4.6-h2dba641_1", "type": 1}}