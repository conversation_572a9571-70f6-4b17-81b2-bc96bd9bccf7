{"build": "conda_forge", "build_number": 0, "depends": [], "license": "None", "md5": "d7c89558ba9fa0495403155b64376d81", "name": "_libgcc_mutex", "purls": [], "sha256": "fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726", "size": 2562, "subdir": "linux-64", "timestamp": 1578324546067, "version": "0.1", "fn": "_libgcc_mutex-0.1-conda_forge.tar.bz2", "url": "https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2", "channel": "https://conda.anaconda.org/conda-forge/", "extracted_package_dir": "/home/<USER>/.cache/rattler/cache/pkgs/_libgcc_mutex-0.1-conda_forge", "files": [], "paths_data": {"paths_version": 1, "paths": []}, "link": {"source": "/home/<USER>/.cache/rattler/cache/pkgs/_libgcc_mutex-0.1-conda_forge", "type": 1}}