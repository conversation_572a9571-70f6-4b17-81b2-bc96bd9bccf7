# pkg-config file generated by gen-pkgconfig
# vile:makemode

prefix=/home/<USER>/repos/lofi-channel/.pixi/envs/default
exec_prefix=${prefix}
libdir=${exec_prefix}/lib
includedir=${prefix}/include/ncursesw
abi_version=6
major_version=6
version=6.5.20240427

Name: panelw
Description: ncurses 6.5 add-on library
Version: ${version}
URL: https://invisible-island.net/ncurses
Requires.private: ncursesw
Libs:  -L/home/<USER>/repos/lofi-channel/.pixi/envs/default/lib -Wl,-O2 -Wl,--sort-common -Wl,--disable-new-dtags -Wl,--gc-sections -Wl,--allow-shlib-undefined -Wl,-rpath,/home/<USER>/repos/lofi-channel/.pixi/envs/default/lib -Wl,-rpath-link,/home/<USER>/repos/lofi-channel/.pixi/envs/default/lib -lpanelw
Libs.private:  
Cflags:  -D_GNU_SOURCE -DNCURSES_WIDECHAR -I${includedir} -I/home/<USER>/repos/lofi-channel/.pixi/envs/default/include
