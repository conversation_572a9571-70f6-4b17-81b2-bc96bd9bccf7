"""
Example usage of the music sources system for lo-fi channel application.
"""

import asyncio
import os
from pathlib import Path

from src.music_sources.manager import MusicSourceManager
from src.music_sources.base import SearchQuery, LicenseType


async def main():
    """Example usage of the music sources system."""
    
    # Initialize the music source manager
    manager = MusicSourceManager(cache_dir="music_cache")
    
    # Configure Freesound (you'll need to get an API key from https://freesound.org/apiv2/apply)
    freesound_api_key = os.getenv("FREESOUND_API_KEY")
    if not freesound_api_key:
        print("Please set FREESOUND_API_KEY environment variable")
        print("Get your API key from: https://freesound.org/apiv2/apply")
        return
    
    manager.configure_freesound(freesound_api_key)
    
    print("🎵 Lo-Fi Channel Music Discovery Demo")
    print("=" * 50)
    
    try:
        # Example 1: Discover lo-fi tracks
        print("\n1. Discovering lo-fi tracks...")
        lofi_tracks = await manager.discover_lofi_tracks(limit=10)
        
        print(f"Found {len(lofi_tracks)} lo-fi tracks:")
        for i, track in enumerate(lofi_tracks[:5], 1):
            print(f"  {i}. {track.title} by {track.artist}")
            print(f"     Duration: {track.duration:.1f}s | License: {track.license_type.value}")
            print(f"     Source: {track.source} | Tags: {', '.join(track.tags[:3])}")
            print()
        
        # Example 2: Search for specific mood
        print("\n2. Searching for chill ambient tracks...")
        chill_query = SearchQuery(
            query="chill ambient",
            mood="relaxing",
            license_types=[LicenseType.CC0, LicenseType.CC_BY],
            min_duration=120.0,  # At least 2 minutes
            limit=5
        )
        
        search_results = await manager.search_all_sources(chill_query)
        
        for source_name, result in search_results.items():
            print(f"\n{source_name} results ({result.total_count} total):")
            for track in result.tracks[:3]:
                print(f"  • {track.title} by {track.artist} ({track.duration:.1f}s)")
        
        # Example 3: Check commercial usage
        if lofi_tracks:
            print("\n3. Commercial usage analysis...")
            commercial_tracks = manager.get_commercial_tracks(lofi_tracks)
            print(f"Tracks suitable for commercial use: {len(commercial_tracks)}/{len(lofi_tracks)}")
            
            # Show attribution requirements
            attributions = manager.get_attribution_requirements(lofi_tracks[:3])
            if attributions:
                print("\nAttribution requirements:")
                for attribution in attributions:
                    print(f"  • {attribution}")
            else:
                print("No attribution required for these tracks!")
        
        # Example 4: Download preview
        if lofi_tracks:
            print("\n4. Downloading preview...")
            first_track = lofi_tracks[0]
            print(f"Downloading preview for: {first_track.title}")
            
            audio_data = await manager.get_audio_preview(first_track)
            if audio_data:
                # Save preview to file
                preview_dir = Path("previews")
                preview_dir.mkdir(exist_ok=True)
                
                filename = f"{first_track.source}_{first_track.id}_preview.mp3"
                filepath = preview_dir / filename
                
                with open(filepath, 'wb') as f:
                    f.write(audio_data)
                
                print(f"Preview saved to: {filepath}")
                print(f"File size: {len(audio_data)} bytes")
            else:
                print("Failed to download preview")
        
        # Example 5: Advanced search
        print("\n5. Advanced search for specific BPM range...")
        # Note: BPM filtering would need to be implemented in the specific source clients
        # This is just an example of how you might structure such a query
        
        advanced_query = SearchQuery(
            query="lofi beats",
            tags=['instrumental', 'hip-hop', 'chill'],
            license_types=[LicenseType.CC0],  # Only public domain
            min_duration=180.0,  # At least 3 minutes
            max_duration=360.0,  # At most 6 minutes
            limit=3
        )
        
        advanced_results = await manager.search_all_sources(advanced_query)
        
        for source_name, result in advanced_results.items():
            if result.tracks:
                print(f"\n{source_name} advanced results:")
                for track in result.tracks:
                    print(f"  • {track.title} ({track.duration:.1f}s)")
                    print(f"    License: {track.license_type.value} (No attribution required!)")
    
    except Exception as e:
        print(f"Error: {e}")
    
    finally:
        # Clean up
        await manager.close()
        print("\n✅ Demo completed!")


def setup_environment():
    """Setup instructions for running the demo."""
    print("🔧 Setup Instructions:")
    print("1. Get a Freesound API key from: https://freesound.org/apiv2/apply")
    print("2. Set environment variable: export FREESOUND_API_KEY='your_key_here'")
    print("3. Install required packages: pip install aiohttp aiofiles")
    print("4. Run this script: python example_usage.py")
    print()


if __name__ == "__main__":
    # Check if API key is available
    if not os.getenv("FREESOUND_API_KEY"):
        setup_environment()
    else:
        asyncio.run(main())
