#!/usr/bin/env python3
"""
Setup script for lo-fi video generator.
"""

import os
import subprocess
import sys
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 9):
        print("❌ Python 3.9 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    return True


def check_ffmpeg():
    """Check if FFmpeg is installed."""
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✅ FFmpeg is installed")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg not found")
        print("Please install FFmpeg:")
        print("  Ubuntu/Debian: sudo apt install ffmpeg")
        print("  macOS: brew install ffmpeg")
        print("  Windows: Download from https://ffmpeg.org/")
        return False


def setup_environment():
    """Setup environment file."""
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    if env_example.exists():
        # Copy example to .env
        with open(env_example) as f:
            content = f.read()
        
        with open(env_file, 'w') as f:
            f.write(content)
        
        print("✅ Created .env file from template")
        print("📝 Please edit .env and add your API keys")
        return True
    else:
        print("❌ .env.example not found")
        return False


def install_dependencies():
    """Install Python dependencies."""
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', '-e', '.'], check=True)
        print("✅ Python dependencies installed")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False


def main():
    """Main setup function."""
    print("🎵 Lo-Fi Video Generator Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check FFmpeg
    if not check_ffmpeg():
        return False
    
    # Install dependencies
    if not install_dependencies():
        return False
    
    # Setup environment
    if not setup_environment():
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\nNext steps:")
    print("1. Edit .env file and add your Freesound API key")
    print("2. Get API key from: https://freesound.org/apiv2/apply")
    print("3. Run: python lofi_cli.py list-styles")
    print("4. Generate video: python lofi_cli.py generate 120 calming")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
