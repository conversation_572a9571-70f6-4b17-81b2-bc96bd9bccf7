# Lo-Fi Video Generator - Quick Start

Generate lo-fi music videos with a simple CLI command!

## 🚀 Quick Setup

1. **Get Freesound API Key** (free):
   ```bash
   # Visit: https://freesound.org/apiv2/apply
   # Register and get your API key
   export FREESOUND_API_KEY='your_key_here'
   ```

2. **Install FFmpeg**:
   ```bash
   # Ubuntu/Debian
   sudo apt install ffmpeg
   
   # macOS
   brew install ffmpeg
   
   # Windows: Download from https://ffmpeg.org/
   ```

3. **Setup Project**:
   ```bash
   python3 setup.py
   # Edit .env file with your API key
   ```

## 🎵 Generate Videos

### Basic Usage
```bash
# 2-minute calming video
python3 lofi_cli.py generate 120 calming

# 5-minute upbeat video
python3 lofi_cli.py generate 300 upbeat

# Custom filename
python3 lofi_cli.py generate 180 chill -o my_chill_video.mp4
```

### Available Styles
- **upbeat** - Energetic, positive vibes
- **calming** - Peaceful, serene sounds
- **chill** - Laid-back, relaxed atmosphere
- **dreamy** - Ethereal, floating melodies
- **nostalgic** - Vintage, retro feelings
- **focus** - Concentration, productivity music
- **study** - Background music for learning
- **relaxing** - Stress-relief, meditation
- **ambient** - Atmospheric soundscapes
- **jazzy** - Jazz-influenced lo-fi

### Explore Options
```bash
# List all styles
python3 lofi_cli.py list-styles

# Preview tracks for a style
python3 lofi_cli.py preview chill

# Show help
python3 lofi_cli.py --help
```

## 📁 Output

Videos are saved in the `videos/` directory with:
- **1920x1080 HD resolution**
- **Style-specific visuals and colors**
- **Track title and artist overlay**
- **Proper attribution (when required)**
- **MP4 format**

## 🎯 Examples

```bash
# Quick 2-minute focus music for work
python3 lofi_cli.py generate 120 focus

# Long study session (10 minutes)
python3 lofi_cli.py generate 600 study

# Dreamy ambient video for relaxation
python3 lofi_cli.py generate 240 dreamy

# Upbeat lo-fi for creative work
python3 lofi_cli.py generate 300 upbeat -o creative_vibes.mp4
```

## 📜 License Compliance

The tool automatically handles licensing:
- **CC0**: Public domain ✅ (no attribution needed)
- **CC-BY**: Attribution required ✅ (shown in CLI output)
- **CC-BY-NC**: Non-commercial only ⚠️

Attribution text is displayed after generation and should be included in video descriptions when required.

## 🔧 Troubleshooting

### "No suitable tracks found"
- Try a different style
- Check your internet connection
- Verify API key is set correctly

### "FFmpeg not found"
- Install FFmpeg (see setup instructions above)
- Make sure it's in your PATH

### "API key error"
- Get a free key from https://freesound.org/apiv2/apply
- Set environment variable: `export FREESOUND_API_KEY='your_key'`
- Or add to `.env` file

### Import errors
- Run from the project root directory
- Make sure all dependencies are installed: `python3 setup.py`

## 🎨 Customization

The tool focuses specifically on **lo-fi music only** and includes:
- Curated lo-fi track discovery
- Style-specific visual themes
- Automatic quality filtering
- Commercial-use friendly licensing

For more advanced features, check the full documentation in `README.md`.

## 🎉 That's it!

You're ready to generate beautiful lo-fi music videos. Start with:

```bash
python3 lofi_cli.py generate 120 calming
```

Happy creating! 🎵✨
