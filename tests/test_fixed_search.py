#!/usr/bin/env python3
"""
Test the fixed search functionality.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.lofi_manager import LoFiMusicManager
from src.music_sources.base import LoFiStyle


async def test_style_search(api_key, style_name):
    """Test searching for a specific style."""
    print(f"\n🎵 Testing {style_name} style search...")
    
    manager = LoFiMusicManager()
    manager.configure_sources(api_key)
    
    try:
        style = LoFiMusicManager.parse_style(style_name)
        tracks = await manager.find_lofi_for_style(style, 120.0, limit=5)
        
        print(f"Found {len(tracks)} tracks for {style_name}:")
        for i, track in enumerate(tracks, 1):
            print(f"  {i}. {track.title} by {track.artist}")
            print(f"     Duration: {track.duration:.1f}s | License: {track.license_type.value}")
            print(f"     Tags: {', '.join(track.tags[:5])}")
            print()
        
        return len(tracks) > 0
        
    except Exception as e:
        print(f"❌ Error testing {style_name}: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await manager.close()


async def test_video_track_selection(api_key):
    """Test getting a track for video generation."""
    print(f"\n🎬 Testing video track selection...")
    
    manager = LoFiMusicManager()
    manager.configure_sources(api_key)
    
    try:
        track = await manager.get_track_for_video(120.0, LoFiStyle.CHILL)
        
        if track:
            print(f"✅ Selected track: {track.title} by {track.artist}")
            print(f"   Duration: {track.duration:.1f}s")
            print(f"   License: {track.license_type.value}")
            print(f"   Source: {track.source}")
            
            # Test attribution
            attribution = manager.get_attribution_text(track)
            if attribution:
                print(f"   Attribution: {attribution[0]}")
            else:
                print("   No attribution required")
            
            return True
        else:
            print("❌ No track found")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await manager.close()


async def main():
    """Run tests with the fixed search."""
    api_key = "jj3e5RIhZONwgJ6bu4wHr9RH3OmodZAdnpycLTQf"
    
    print("🔧 Testing Fixed Lo-Fi Search")
    print("=" * 40)
    
    # Test different styles
    styles_to_test = ['chill', 'calming', 'upbeat', 'study']
    
    passed = 0
    total = len(styles_to_test) + 1  # +1 for video track test
    
    for style in styles_to_test:
        if await test_style_search(api_key, style):
            passed += 1
    
    # Test video track selection
    if await test_video_track_selection(api_key):
        passed += 1
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed > 0:
        print("🎉 Search is working! You can now generate videos.")
        print("\nTry: python3 lofi_cli.py generate 120 chill")
    else:
        print("❌ Search still not working. Need more debugging.")


if __name__ == "__main__":
    asyncio.run(main())
