#!/usr/bin/env python3
"""
Test script for the lo-fi CLI.
"""

import subprocess
import sys
from pathlib import Path


def test_cli_help():
    """Test CLI help command."""
    try:
        result = subprocess.run([sys.executable, 'lofi_cli.py', '--help'], 
                              capture_output=True, text=True, timeout=10)
        print("✅ CLI help works")
        return True
    except Exception as e:
        print(f"❌ CLI help failed: {e}")
        return False


def test_list_styles():
    """Test list styles command."""
    try:
        result = subprocess.run([sys.executable, 'lofi_cli.py', 'list-styles'], 
                              capture_output=True, text=True, timeout=10)
        if 'upbeat' in result.stdout and 'calming' in result.stdout:
            print("✅ List styles works")
            return True
        else:
            print(f"❌ List styles output unexpected: {result.stdout}")
            return False
    except Exception as e:
        print(f"❌ List styles failed: {e}")
        return False


def test_imports():
    """Test that all imports work."""
    try:
        import src.lofi_manager
        import src.video_generator
        import src.music_sources.base
        import src.music_sources.freesound
        import src.music_sources.manager
        print("✅ All imports work")
        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


def main():
    """Run basic tests."""
    print("🧪 Testing Lo-Fi CLI")
    print("=" * 30)
    
    tests = [
        test_imports,
        test_cli_help,
        test_list_styles,
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print(f"\n📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed!")
        return True
    else:
        print("❌ Some tests failed")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
