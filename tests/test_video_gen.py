#!/usr/bin/env python3
"""
Test video generation without FFmpeg.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.lofi_manager import LoFiMusicManager
from src.music_sources.base import LoFiStyle


async def test_track_finding():
    """Test if we can find tracks for video generation."""
    api_key = "jj3e5RIhZONwgJ6bu4wHr9RH3OmodZAdnpycLTQf"
    
    print("🎵 Testing track finding for video generation...")
    
    manager = LoFiMusicManager()
    manager.configure_sources(api_key)
    
    try:
        # Test different styles
        styles = [LoFiStyle.CHILL, LoFiStyle.CALMING, LoFiStyle.UPBEAT, LoFiStyle.STUDY]
        
        for style in styles:
            print(f"\n🎨 Testing {style.value} style...")
            track = await manager.get_track_for_video(120.0, style)
            
            if track:
                print(f"✅ Found: {track.title} by {track.artist}")
                print(f"   Duration: {track.duration:.1f}s")
                print(f"   License: {track.license_type.value}")
                
                # Test audio download
                print("   Downloading audio preview...")
                audio_data = await manager.download_track_audio(track)
                if audio_data:
                    print(f"   ✅ Downloaded {len(audio_data)} bytes")
                else:
                    print("   ❌ Failed to download audio")
            else:
                print(f"❌ No track found for {style.value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        await manager.close()


if __name__ == "__main__":
    asyncio.run(test_track_finding())
