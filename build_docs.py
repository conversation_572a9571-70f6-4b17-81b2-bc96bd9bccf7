#!/usr/bin/env python3
"""
Build script for Lo-Fi Video Generator documentation.
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """Run a command and handle errors."""
    print(f"🔧 {description}")
    print(f"Command: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("✅ Success")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def check_dependencies():
    """Check if documentation dependencies are installed."""
    print("📋 Checking documentation dependencies...")
    
    required_packages = [
        'sphinx',
        'sphinx_rtd_theme',
        'myst_parser'
    ]
    
    missing = []
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n📦 Install missing packages:")
        print(f"pip install {' '.join(missing)}")
        print("Or install all documentation dependencies:")
        print("pip install -e '.[docs]'")
        return False
    
    return True


def build_docs():
    """Build the documentation."""
    docs_dir = Path("docs")
    build_dir = docs_dir / "_build"
    
    if not docs_dir.exists():
        print("❌ docs/ directory not found")
        return False
    
    # Change to docs directory
    os.chdir(docs_dir)
    
    # Clean previous build
    if build_dir.exists():
        print("🧹 Cleaning previous build...")
        run_command(["rm", "-rf", "_build"], "Removing old build files")
    
    # Build HTML documentation
    success = run_command(
        ["sphinx-build", "-b", "html", ".", "_build/html"],
        "Building HTML documentation"
    )
    
    if success:
        html_path = build_dir / "html" / "index.html"
        print(f"\n🎉 Documentation built successfully!")
        print(f"📁 Output: {html_path.absolute()}")
        print(f"🌐 Open: file://{html_path.absolute()}")
        
        # Try to open in browser
        try:
            import webbrowser
            webbrowser.open(f"file://{html_path.absolute()}")
            print("🔗 Opened in browser")
        except Exception:
            print("💡 Manually open the HTML file in your browser")
    
    return success


def serve_docs():
    """Serve documentation with live reload."""
    try:
        import sphinx_autobuild
    except ImportError:
        print("❌ sphinx-autobuild not installed")
        print("Install with: pip install sphinx-autobuild")
        return False
    
    print("🚀 Starting live documentation server...")
    print("📝 Edit files and see changes automatically")
    print("🛑 Press Ctrl+C to stop")
    
    os.chdir("docs")
    
    cmd = [
        "sphinx-autobuild",
        ".",
        "_build/html",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--open-browser"
    ]
    
    try:
        subprocess.run(cmd, check=True)
    except KeyboardInterrupt:
        print("\n👋 Documentation server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Server failed: {e}")
        return False
    
    return True


def check_links():
    """Check for broken links in documentation."""
    print("🔗 Checking for broken links...")
    
    os.chdir("docs")
    
    success = run_command(
        ["sphinx-build", "-b", "linkcheck", ".", "_build/linkcheck"],
        "Checking links"
    )
    
    if success:
        linkcheck_output = Path("_build/linkcheck/output.txt")
        if linkcheck_output.exists():
            with open(linkcheck_output) as f:
                content = f.read()
                if "broken" in content.lower():
                    print("⚠️ Some broken links found. Check _build/linkcheck/output.txt")
                else:
                    print("✅ No broken links found")
    
    return success


def main():
    """Main build script."""
    if len(sys.argv) < 2:
        print("📖 Lo-Fi Video Generator Documentation Builder")
        print("\nUsage:")
        print("  python build_docs.py build    - Build documentation")
        print("  python build_docs.py serve    - Serve with live reload")
        print("  python build_docs.py check    - Check dependencies")
        print("  python build_docs.py links    - Check for broken links")
        print("  python build_docs.py clean    - Clean build files")
        return
    
    command = sys.argv[1]
    
    if command == "check":
        if check_dependencies():
            print("✅ All dependencies available")
        else:
            sys.exit(1)
    
    elif command == "build":
        if not check_dependencies():
            sys.exit(1)
        
        if build_docs():
            print("✅ Documentation build completed")
        else:
            print("❌ Documentation build failed")
            sys.exit(1)
    
    elif command == "serve":
        if not check_dependencies():
            sys.exit(1)
        
        serve_docs()
    
    elif command == "links":
        if not check_dependencies():
            sys.exit(1)
        
        check_links()
    
    elif command == "clean":
        docs_dir = Path("docs")
        build_dir = docs_dir / "_build"
        
        if build_dir.exists():
            run_command(["rm", "-rf", str(build_dir)], "Cleaning build files")
            print("✅ Build files cleaned")
        else:
            print("✅ No build files to clean")
    
    else:
        print(f"❌ Unknown command: {command}")
        sys.exit(1)


if __name__ == "__main__":
    main()
