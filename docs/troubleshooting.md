# Troubleshooting

Common issues and solutions for the Lo-Fi Video Generator.

## Installation Issues

### Python Version Error

**Problem**: `Python 3.9 or higher is required`

**Solution**:
```bash
# Check your Python version
python3 --version

# Install Python 3.9+ if needed
# Ubuntu/Debian
sudo apt update
sudo apt install python3.9

# macOS
brew install python@3.9

# Or use pyenv
pyenv install 3.9.0
pyenv global 3.9.0
```

### FFmpeg Not Found

**Problem**: `❌ FFmpeg not found`

**Solutions**:

#### Ubuntu/Debian
```bash
sudo apt update
sudo apt install ffmpeg

# Verify installation
ffmpeg -version
```

#### macOS
```bash
# Using Homebrew
brew install ffmpeg

# Using MacPorts
sudo port install ffmpeg
```

#### Windows
1. Download from [https://ffmpeg.org/download.html](https://ffmpeg.org/download.html)
2. Extract to `C:\ffmpeg`
3. Add `C:\ffmpeg\bin` to system PATH
4. Restart terminal/command prompt

#### Verify Installation
```bash
ffmpeg -version
which ffmpeg  # Should show path to ffmpeg
```

### Dependency Installation Failed

**Problem**: `Failed to install dependencies`

**Solutions**:
```bash
# Update pip first
python3 -m pip install --upgrade pip

# Install manually
pip install aiohttp aiofiles python-dotenv

# Or with specific versions
pip install aiohttp>=3.8.0 aiofiles>=23.0.0 python-dotenv>=1.0.0

# If using conda
conda install aiohttp aiofiles python-dotenv
```

## API Issues

### API Key Not Found

**Problem**: `❌ Error: FREESOUND_API_KEY not found in environment`

**Solutions**:

#### Option 1: Environment Variable
```bash
export FREESOUND_API_KEY='your_api_key_here'

# Make it permanent (add to ~/.bashrc or ~/.zshrc)
echo 'export FREESOUND_API_KEY="your_api_key_here"' >> ~/.bashrc
source ~/.bashrc
```

#### Option 2: .env File
```bash
# Create .env file in project root
echo 'FREESOUND_API_KEY="your_api_key_here"' > .env

# Or copy from template
cp .env.example .env
# Edit .env file with your key
```

#### Option 3: Verify API Key
```bash
# Check if key is set
echo $FREESOUND_API_KEY

# Test API key validity
python3 debug_api.py
```

### Invalid API Key

**Problem**: `❌ API connection failed: Invalid API key`

**Solutions**:
1. **Get a new API key**: Visit [https://freesound.org/apiv2/apply](https://freesound.org/apiv2/apply)
2. **Check key format**: Should be 40 characters long
3. **Verify account status**: Ensure your Freesound account is active
4. **Test with curl**:
   ```bash
   curl "https://freesound.org/apiv2/search/text/?query=test&token=YOUR_API_KEY"
   ```

### Rate Limit Exceeded

**Problem**: `❌ Rate limit exceeded`

**Solutions**:
- **Wait**: Freesound allows 60 requests/minute, 2000/day
- **Check usage**: The tool has built-in rate limiting
- **Upgrade account**: Consider Freesound premium for higher limits
- **Use caching**: Cached results don't count against limits

## Search Issues

### No Tracks Found

**Problem**: `❌ No suitable lo-fi tracks found`

**Solutions**:

#### Try Different Styles
```bash
# If one style fails, try others
python3 lofi_cli.py generate 120 chill
python3 lofi_cli.py generate 120 study
python3 lofi_cli.py generate 120 ambient
```

#### Adjust Duration
```bash
# Try shorter duration
python3 lofi_cli.py generate 60 chill

# Try longer duration
python3 lofi_cli.py generate 300 chill
```

#### Check Internet Connection
```bash
# Test basic connectivity
ping google.com

# Test Freesound API
curl "https://freesound.org/apiv2/search/text/?query=lofi&token=YOUR_API_KEY"
```

#### Debug Search
```bash
# Run debug script
python3 debug_api.py

# Test specific search
python3 test_fixed_search.py
```

### Search Takes Too Long

**Problem**: Search hangs or takes very long

**Solutions**:
- **Check internet speed**: Slow connection affects API calls
- **Clear cache**: Remove `lofi_cache/` directory
- **Restart**: Kill and restart the process
- **Use timeout**: The tool has built-in timeouts

## Video Generation Issues

### Video Generation Failed

**Problem**: `❌ Failed to generate video`

**Solutions**:

#### Check FFmpeg
```bash
# Verify FFmpeg works
ffmpeg -f lavfi -i testsrc=duration=1:size=320x240:rate=1 test.mp4

# Check FFmpeg version (should be recent)
ffmpeg -version
```

#### Check Disk Space
```bash
# Check available space
df -h .

# Clean up old videos if needed
rm videos/*.mp4
```

#### Check Permissions
```bash
# Ensure write permissions
ls -la videos/
chmod 755 videos/

# Create videos directory if missing
mkdir -p videos
```

#### Debug Video Generation
```bash
# Test without video generation
python3 test_video_gen.py

# Generate with verbose output
python3 lofi_cli.py generate 30 chill -o test.mp4
```

### Audio Download Failed

**Problem**: `❌ Failed to download audio`

**Solutions**:
- **Check internet**: Ensure stable connection
- **Try different track**: Some tracks may be unavailable
- **Clear cache**: Remove cached files
- **Check API limits**: May have hit daily limit

### Video Quality Issues

**Problem**: Poor video quality or corruption

**Solutions**:
```bash
# Check FFmpeg encoding settings
ffmpeg -encoders | grep h264

# Regenerate video
rm videos/problematic_video.mp4
python3 lofi_cli.py generate 120 chill -o new_video.mp4

# Check video info
ffprobe videos/your_video.mp4
```

## Performance Issues

### Slow Performance

**Problem**: Video generation takes very long

**Solutions**:

#### Optimize Cache
```bash
# Clear old cache
rm -rf lofi_cache/
mkdir lofi_cache

# Set cache size limit in .env
echo 'MAX_CACHE_SIZE_MB=500' >> .env
```

#### Reduce Video Duration
```bash
# Generate shorter videos for testing
python3 lofi_cli.py generate 30 chill
```

#### Check System Resources
```bash
# Monitor CPU/memory usage
top
htop

# Check disk I/O
iotop
```

### Memory Issues

**Problem**: Out of memory errors

**Solutions**:
- **Close other applications**: Free up RAM
- **Generate shorter videos**: Reduce memory usage
- **Check available memory**: `free -h`
- **Restart system**: Clear memory leaks

## Import/Module Issues

### Import Errors

**Problem**: `ModuleNotFoundError` or import issues

**Solutions**:

#### Check Working Directory
```bash
# Run from project root
cd /path/to/lofi-channel
python3 lofi_cli.py --help
```

#### Reinstall Dependencies
```bash
# Clean install
pip uninstall -y aiohttp aiofiles python-dotenv
pip install -e .
```

#### Check Python Path
```bash
# Verify Python can find modules
python3 -c "import src.lofi_manager; print('OK')"
```

### Relative Import Issues

**Problem**: `ImportError: attempted relative import`

**Solutions**:
```bash
# Always run from project root
cd lofi-channel
python3 lofi_cli.py generate 120 chill

# Don't run scripts from subdirectories
```

## CLI Issues

### Command Not Found

**Problem**: `lofi_cli.py: command not found`

**Solutions**:
```bash
# Use full path
python3 /path/to/lofi-channel/lofi_cli.py generate 120 chill

# Or run from project directory
cd lofi-channel
python3 lofi_cli.py generate 120 chill

# Make executable (optional)
chmod +x lofi_cli.py
./lofi_cli.py generate 120 chill
```

### Invalid Arguments

**Problem**: `Invalid style` or argument errors

**Solutions**:
```bash
# Check available styles
python3 lofi_cli.py list-styles

# Use exact style names
python3 lofi_cli.py generate 120 chill  # not "Chill" or "CHILL"

# Check command syntax
python3 lofi_cli.py generate --help
```

## Debug Tools

### Built-in Debug Scripts

```bash
# Test API connection
python3 debug_api.py

# Test track finding
python3 test_video_gen.py

# Test CLI functionality
python3 test_cli.py

# Test fixed search
python3 test_fixed_search.py
```

### Manual Testing

```bash
# Test individual components
python3 -c "
import asyncio
from src.music_sources.freesound import FreesoundClient
async def test():
    client = FreesoundClient('your_api_key')
    result = await client._make_request('search/text/', {'query': 'test'})
    print(f'Found {result.get(\"count\", 0)} results')
    await client.close()
asyncio.run(test())
"
```

### Logging

Enable verbose logging by setting environment variable:
```bash
export LOFI_DEBUG=1
python3 lofi_cli.py generate 120 chill
```

## Getting Help

### Check Documentation
- [Installation Guide](installation.md)
- [Quick Start](quickstart.md)
- [CLI Reference](cli-reference.md)

### Community Support
- GitHub Issues: Report bugs and get help
- Discussions: Ask questions and share tips

### Diagnostic Information

When reporting issues, include:

```bash
# System information
python3 --version
ffmpeg -version
uname -a

# Project status
python3 test_cli.py
ls -la videos/
ls -la lofi_cache/

# Error logs
python3 lofi_cli.py generate 120 chill 2>&1 | tee error.log
```

## Common Solutions Summary

| Problem | Quick Fix |
|---------|-----------|
| **FFmpeg not found** | `sudo apt install ffmpeg` (Ubuntu) or `brew install ffmpeg` (macOS) |
| **API key error** | `export FREESOUND_API_KEY='your_key'` |
| **No tracks found** | Try different style or duration |
| **Import errors** | Run from project root directory |
| **Slow performance** | Clear cache: `rm -rf lofi_cache/` |
| **Video generation fails** | Check disk space and permissions |
| **Rate limit** | Wait or use cached results |

Most issues can be resolved by ensuring:
1. ✅ Python 3.9+ installed
2. ✅ FFmpeg installed and in PATH
3. ✅ Valid Freesound API key set
4. ✅ Running from project root directory
5. ✅ Stable internet connection
