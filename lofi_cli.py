#!/usr/bin/env python3
"""
Lo-Fi Video Generator CLI

A command-line interface for generating lo-fi music videos.
"""

import asyncio
import argparse
import os
import sys
from pathlib import Path
from typing import Optional

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.lofi_manager import LoFiMusicManager
from src.video_generator import LoFiVideoGenerator
from src.music_sources.base import LoFiStyle


class LoFiCLI:
    """Command-line interface for lo-fi video generation."""
    
    def __init__(self):
        self.music_manager = None
        self.video_generator = None
    
    async def setup(self):
        """Initialize managers and check dependencies."""
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        # Initialize music manager
        self.music_manager = LoFiMusicManager()
        
        # Configure music sources
        freesound_key = os.getenv("FREESOUND_API_KEY")
        if not freesound_key:
            print("❌ Error: FREESOUND_API_KEY not found in environment")
            print("Please set your Freesound API key:")
            print("export FREESOUND_API_KEY='your_key_here'")
            print("Get your key at: https://freesound.org/apiv2/apply")
            return False
        
        self.music_manager.configure_sources(freesound_key)
        
        # Initialize video generator
        self.video_generator = LoFiVideoGenerator()
        
        # Check video dependencies
        deps_ok, missing = self.video_generator.check_dependencies()
        if not deps_ok:
            print("❌ Missing dependencies:")
            for dep in missing:
                print(f"  - {dep}")
            print("\nPlease install FFmpeg:")
            print("  Ubuntu/Debian: sudo apt install ffmpeg")
            print("  macOS: brew install ffmpeg")
            print("  Windows: Download from https://ffmpeg.org/")
            return False
        
        return True
    
    async def generate_video(self, duration: float, style: str, output: Optional[str] = None):
        """Generate a lo-fi video with specified duration and style."""
        
        # Parse style
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            print(f"❌ Invalid style: {style}")
            print(f"Available styles: {', '.join(LoFiMusicManager.get_available_styles())}")
            return False
        
        print(f"🎵 Generating {duration}s lo-fi video with {style} style...")
        print("=" * 50)
        
        try:
            # Find suitable track
            print("🔍 Searching for lo-fi music...")
            track = await self.music_manager.get_track_for_video(duration, lofi_style)
            
            if not track:
                print("❌ No suitable lo-fi tracks found")
                print("Try a different style or duration")
                return False
            
            print(f"✅ Found track: '{track.title}' by {track.artist}")
            print(f"   Duration: {track.duration:.1f}s | License: {track.license_type.value}")
            print(f"   Source: {track.source}")
            
            # Download audio
            print("⬇️  Downloading audio...")
            audio_data = await self.music_manager.download_track_audio(track)
            
            if not audio_data:
                print("❌ Failed to download audio")
                return False
            
            print(f"✅ Downloaded {len(audio_data)} bytes of audio data")
            
            # Generate video
            print("🎬 Generating video...")
            video_path = await self.video_generator.generate_video(
                track, audio_data, duration, lofi_style, output
            )
            
            if not video_path:
                print("❌ Failed to generate video")
                return False
            
            print(f"✅ Video generated successfully!")
            print(f"📁 Output: {video_path}")
            
            # Show attribution if required
            attribution = self.music_manager.get_attribution_text(track)
            if attribution:
                print("\n📜 Attribution required:")
                for attr in attribution:
                    print(f"   {attr}")
            else:
                print("\n✅ No attribution required (public domain)")
            
            # Show video info
            video_info = self.video_generator.get_video_info(video_path)
            if video_info:
                format_info = video_info.get('format', {})
                file_size = int(format_info.get('size', 0))
                if file_size > 0:
                    print(f"📊 File size: {file_size / (1024*1024):.1f} MB")
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    async def list_styles(self):
        """List available lo-fi styles."""
        print("🎨 Available Lo-Fi Styles:")
        print("=" * 30)
        
        styles = LoFiMusicManager.get_available_styles()
        for style in styles:
            style_enum = LoFiMusicManager.parse_style(style)
            keywords = LoFiMusicManager.STYLE_KEYWORDS.get(style_enum, [])
            print(f"  {style:<12} - {', '.join(keywords[:3])}")
    
    async def preview_style(self, style: str, count: int = 5):
        """Preview tracks available for a style."""
        lofi_style = LoFiMusicManager.parse_style(style)
        if not lofi_style:
            print(f"❌ Invalid style: {style}")
            return False
        
        print(f"🎵 Preview of {style} lo-fi tracks:")
        print("=" * 40)
        
        try:
            tracks = await self.music_manager.find_lofi_for_style(lofi_style, 180.0, limit=count)
            
            if not tracks:
                print("❌ No tracks found for this style")
                return False
            
            for i, track in enumerate(tracks, 1):
                print(f"{i}. {track.title}")
                print(f"   Artist: {track.artist}")
                print(f"   Duration: {track.duration:.1f}s")
                print(f"   Tags: {', '.join(track.tags[:5])}")
                print()
            
            return True
            
        except Exception as e:
            print(f"❌ Error: {e}")
            return False
    
    async def cleanup(self):
        """Clean up resources."""
        if self.music_manager:
            await self.music_manager.close()
        if self.video_generator:
            self.video_generator.cleanup_temp_files()


def main():
    """Main CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Generate lo-fi music videos",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s generate 120 calming                    # 2-minute calming video
  %(prog)s generate 300 upbeat -o my_video.mp4     # 5-minute upbeat video
  %(prog)s list-styles                             # Show available styles
  %(prog)s preview chill                           # Preview chill tracks
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Generate command
    gen_parser = subparsers.add_parser('generate', help='Generate a lo-fi video')
    gen_parser.add_argument('duration', type=float, help='Video duration in seconds')
    gen_parser.add_argument('style', help='Lo-fi style (e.g., calming, upbeat, chill)')
    gen_parser.add_argument('-o', '--output', help='Output filename')
    
    # List styles command
    subparsers.add_parser('list-styles', help='List available lo-fi styles')
    
    # Preview command
    preview_parser = subparsers.add_parser('preview', help='Preview tracks for a style')
    preview_parser.add_argument('style', help='Lo-fi style to preview')
    preview_parser.add_argument('-c', '--count', type=int, default=5, help='Number of tracks to show')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    async def run_cli():
        cli = LoFiCLI()
        
        try:
            if args.command == 'list-styles':
                await cli.list_styles()
                return
            
            # For other commands, we need to setup
            if not await cli.setup():
                return
            
            if args.command == 'generate':
                success = await cli.generate_video(args.duration, args.style, args.output)
                if not success:
                    sys.exit(1)
            
            elif args.command == 'preview':
                success = await cli.preview_style(args.style, args.count)
                if not success:
                    sys.exit(1)
        
        finally:
            await cli.cleanup()
    
    # Run the async CLI
    try:
        asyncio.run(run_cli())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
