#!/usr/bin/env python3
"""
Demo script for the Lo-Fi Video Generator.
"""

import subprocess
import sys
import os
from pathlib import Path


def print_header(title):
    """Print a formatted header."""
    print(f"\n{'='*50}")
    print(f"🎵 {title}")
    print(f"{'='*50}")


def run_command(cmd, description):
    """Run a command and show the output."""
    print(f"\n🔧 {description}")
    print(f"Command: {' '.join(cmd)}")
    print("-" * 40)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        return result.returncode == 0
    except subprocess.TimeoutExpired:
        print("❌ Command timed out")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Run the demo."""
    print_header("Lo-Fi Video Generator Demo")
    
    # Check if we're in the right directory
    if not Path("lofi_cli.py").exists():
        print("❌ Please run this demo from the lofi-channel directory")
        return False
    
    # Check if API key is set
    if not os.getenv("FREESOUND_API_KEY"):
        print("⚠️  FREESOUND_API_KEY not set - some features won't work")
        print("Get your API key from: https://freesound.org/apiv2/apply")
        print("Then run: export FREESOUND_API_KEY='your_key_here'")
    
    print("\n🎯 This demo will show you the lo-fi video generator capabilities")
    
    # Demo 1: Show available styles
    print_header("Available Lo-Fi Styles")
    run_command([sys.executable, "lofi_cli.py", "list-styles"], 
                "Listing all available lo-fi styles")
    
    # Demo 2: Show help
    print_header("CLI Help")
    run_command([sys.executable, "lofi_cli.py", "--help"], 
                "Showing CLI help and usage examples")
    
    # Demo 3: Show generate help
    print_header("Generate Command Help")
    run_command([sys.executable, "lofi_cli.py", "generate", "--help"], 
                "Showing video generation options")
    
    # Demo 4: Preview tracks (if API key is available)
    if os.getenv("FREESOUND_API_KEY"):
        print_header("Preview Tracks")
        run_command([sys.executable, "lofi_cli.py", "preview", "chill", "-c", "3"], 
                    "Previewing chill lo-fi tracks")
        
        print("\n🎬 To generate a video, run:")
        print("python3 lofi_cli.py generate 120 calming")
        print("python3 lofi_cli.py generate 300 upbeat -o my_video.mp4")
    else:
        print_header("API Key Required")
        print("To preview tracks and generate videos, you need a Freesound API key:")
        print("1. Visit: https://freesound.org/apiv2/apply")
        print("2. Register for a free account")
        print("3. Get your API key")
        print("4. Set it: export FREESOUND_API_KEY='your_key_here'")
        print("5. Run: python3 lofi_cli.py preview chill")
    
    print_header("Demo Complete")
    print("🎉 The lo-fi video generator is ready to use!")
    print("\nNext steps:")
    print("1. Get a Freesound API key (if you haven't already)")
    print("2. Install FFmpeg for video generation")
    print("3. Generate your first lo-fi video:")
    print("   python3 lofi_cli.py generate 120 calming")
    print("\n📚 Check README.md for more examples and documentation")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
