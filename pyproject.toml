[project]
name = "lofi-channel"
version = "0.1.0"
description = "A lo-fi music channel application with royalty-free music sources"
readme = "README.md"
requires-python = ">=3.9"
dependencies = [
    "aiohttp>=3.8.0",
    "aiofiles>=23.0.0",
    "python-dotenv>=1.0.0",
    "click>=8.0.0"
]

[project.scripts]
lofi = "lofi_cli:main"

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "flake8>=6.0.0",
    "mypy>=1.0.0",
    "pre-commit>=3.0.0",
]
docs = [
    "sphinx>=5.0.0",
    "sphinx-rtd-theme>=1.2.0",
    "myst-parser>=1.0.0",
    "sphinx-autodoc-typehints>=1.19.0",
    "sphinx-copybutton>=0.5.0",
]

[tool.pixi.project]
channels = ["conda-forge"]
platforms = ["linux-64"]

# [tool.pixi.pypi-dependencies]
# lofi-channel = { path = ".", editable = true }

[tool.pixi.environments]
default = { solve-group = "default" }
dev = { features = ["dev"], solve-group = "default" }

[tool.pixi.tasks]

[tool.pixi.dependencies]
ffmpeg = ">=7.1.1,<8"
